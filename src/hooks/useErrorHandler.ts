import { useAtom } from 'jotai';
import { useCallback, useEffect } from 'react';

import type { ApiError } from '@/api/axios-instance';
import {
  addErrorNotificationAtom,
  clearGlobalErrorAtom,
  createAppError,
  createAppErrorFromApiError,
  type ErrorNotification,
  setGlobalErrorAtom,
  setNetworkStatusAtom,
  setRetryStatusAtom,
} from '@/lib/jotai/error-atoms';

/**
 * 錯誤處理 Hook
 * 提供統一的錯誤處理方法
 */
export const useErrorHandler = () => {
  const [, setGlobalError] = useAtom(setGlobalErrorAtom);
  const [, clearGlobalError] = useAtom(clearGlobalErrorAtom);
  const [, addNotification] = useAtom(addErrorNotificationAtom);
  const [, setRetryStatus] = useAtom(setRetryStatusAtom);
  const [, setNetworkStatus] = useAtom(setNetworkStatusAtom);

  // 處理 API 錯誤
  const handleApiError = useCallback(
    (error: Error & { apiError?: ApiError }, retryFn?: () => Promise<void>) => {
      if (error.apiError) {
        const appError = createAppErrorFromApiError(error.apiError);
        setGlobalError(appError);

        // 如果是可重試的錯誤且提供了重試函數，顯示重試通知
        if (appError.isRetryable && retryFn) {
          addNotification({
            type: 'error',
            title: appError.title,
            message: appError.message,
            duration: 5000,
            actions: [
              {
                label: '重試',
                action: async () => {
                  setRetryStatus({ isRetrying: true, retryingErrorId: appError.id });
                  try {
                    await retryFn();
                    clearGlobalError();
                  } catch (retryError) {
                    console.error('重試失敗:', retryError);
                  } finally {
                    setRetryStatus({ isRetrying: false });
                  }
                },
                variant: 'primary',
              },
            ],
          });
        }
      } else {
        // 處理一般錯誤
        const appError = createAppError('api', '請求失敗', error.message || '發生未知錯誤');
        setGlobalError(appError);
      }
    },
    [setGlobalError, addNotification, setRetryStatus, clearGlobalError],
  );

  // 處理網路錯誤
  const handleNetworkError = useCallback(
    (message = '網路連接失敗') => {
      const appError = createAppError('network', '網路錯誤', message, {
        isRetryable: true,
      });
      setGlobalError(appError);
      setNetworkStatus('offline');
    },
    [setGlobalError, setNetworkStatus],
  );

  // 處理運行時錯誤
  const handleRuntimeError = useCallback(
    (error: Error) => {
      const appError = createAppError(
        'runtime',
        'JavaScript 錯誤',
        error.message || '應用程式發生運行時錯誤',
        {
          stack: error.stack,
        },
      );
      setGlobalError(appError);
    },
    [setGlobalError],
  );

  // 處理路由錯誤
  const handleRouteError = useCallback(
    (message = '頁面載入失敗') => {
      const appError = createAppError('route', '路由錯誤', message, {
        isRetryable: true,
      });
      setGlobalError(appError);
    },
    [setGlobalError],
  );

  // 處理代碼分割載入錯誤
  const handleChunkLoadError = useCallback(() => {
    const appError = createAppError('chunk', '應用程式更新', '應用程式已更新，請重新載入頁面', {
      isRetryable: true,
    });
    setGlobalError(appError);
  }, [setGlobalError]);

  // 顯示成功通知
  const showSuccessNotification = useCallback(
    (title: string, message: string, duration = 3000) => {
      addNotification({
        type: 'info',
        title,
        message,
        duration,
      });
    },
    [addNotification],
  );

  // 顯示警告通知
  const showWarningNotification = useCallback(
    (title: string, message: string, duration = 4000) => {
      addNotification({
        type: 'warning',
        title,
        message,
        duration,
      });
    },
    [addNotification],
  );

  // 顯示錯誤通知
  const showErrorNotification = useCallback(
    (title: string, message: string, actions?: ErrorNotification['actions']) => {
      addNotification({
        type: 'error',
        title,
        message,
        actions,
      });
    },
    [addNotification],
  );

  // 清除錯誤
  const clearError = useCallback(() => {
    clearGlobalError();
  }, [clearGlobalError]);

  // 監聽網路狀態變化
  useEffect(() => {
    const handleOnline = () => setNetworkStatus('online');
    const handleOffline = () => setNetworkStatus('offline');

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, [setNetworkStatus]);

  // 監聽未處理的 Promise 拒絕
  useEffect(() => {
    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      console.error('未處理的 Promise 拒絕:', event.reason);

      if (event.reason instanceof Error) {
        handleRuntimeError(event.reason);
      } else {
        const appError = createAppError(
          'runtime',
          '未處理的錯誤',
          String(event.reason) || '發生未知錯誤',
        );
        setGlobalError(appError);
      }
    };

    window.addEventListener('unhandledrejection', handleUnhandledRejection);

    return () => {
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
    };
  }, [handleRuntimeError, setGlobalError]);

  return {
    handleApiError,
    handleNetworkError,
    handleRuntimeError,
    handleRouteError,
    handleChunkLoadError,
    showSuccessNotification,
    showWarningNotification,
    showErrorNotification,
    clearError,
  };
};
