/**
 *  axios 實例設定與攔截器
 *
 *  - 創建 axios 實例
 *  - 請求攔截器（附帶 token）
 *  - 響應攔截器
 *  - 錯誤處理與重試機制
 */

import axios, {
  AxiosError,
  AxiosInstance,
  AxiosRequestConfig,
  AxiosRequestHeaders,
  InternalAxiosRequestConfig,
} from 'axios';

import { CookieNameEnum } from '@/types/enums/common/cookie';
import { toCamelCase } from '@/utils/common';
import { getCookie } from '@/utils/cookie';

// 錯誤類型定義
export interface ApiError {
  code: string;
  message: string;
  status?: number;
  isRetryable: boolean;
  isNetworkError: boolean;
}

// 重試配置
const RETRY_CONFIG = {
  maxRetries: 3,
  retryDelay: 1000, // 1秒
  retryableStatuses: [408, 429, 500, 502, 503, 504],
  retryableErrors: ['NETWORK_ERROR', 'TIMEOUT', 'ECONNABORTED'],
};

// API 基礎 URL
const API_BASE_URL = '/api'; // TODO 改成環境變數： import.meta.env.VITE_SERVER_ENV_FD_BACKEND_SERVER,

// 創建錯誤對象
const createApiError = (error: AxiosError): ApiError => {
  const status = error.response?.status;
  const isNetworkError = !error.response && error.code !== 'ECONNABORTED';
  const isTimeoutError = error.code === 'ECONNABORTED' || error.message.includes('timeout');

  let code = 'UNKNOWN_ERROR';
  let message = 'API 請求失敗';
  let isRetryable = false;

  if (isNetworkError) {
    code = 'NETWORK_ERROR';
    message = '網路連接失敗，請檢查您的網路連接';
    isRetryable = true;
  } else if (isTimeoutError) {
    code = 'TIMEOUT_ERROR';
    message = '請求超時，請重試';
    isRetryable = true;
  } else if (status) {
    isRetryable = RETRY_CONFIG.retryableStatuses.includes(status);

    switch (status) {
      case 400:
        code = 'BAD_REQUEST';
        message = '請求參數錯誤';
        break;
      case 401:
        code = 'UNAUTHORIZED';
        message = '未授權訪問，請重新登入';
        break;
      case 403:
        code = 'FORBIDDEN';
        message = '權限不足';
        break;
      case 404:
        code = 'NOT_FOUND';
        message = '請求的資源不存在';
        break;
      case 408:
        code = 'REQUEST_TIMEOUT';
        message = '請求超時';
        break;
      case 429:
        code = 'TOO_MANY_REQUESTS';
        message = '請求過於頻繁，請稍後再試';
        break;
      case 500:
        code = 'INTERNAL_SERVER_ERROR';
        message = '服務器內部錯誤';
        break;
      case 502:
        code = 'BAD_GATEWAY';
        message = '網關錯誤';
        break;
      case 503:
        code = 'SERVICE_UNAVAILABLE';
        message = '服務暫時不可用';
        break;
      case 504:
        code = 'GATEWAY_TIMEOUT';
        message = '網關超時';
        break;
      default:
        code = `HTTP_${status}`;
        message = `HTTP 錯誤 ${status}`;
    }
  }

  // 嘗試從響應中獲取更詳細的錯誤信息
  if (error.response?.data && typeof error.response.data === 'object') {
    const responseData = error.response.data as { message?: string };
    if (responseData.message) {
      message = responseData.message;
    }
  }

  return {
    code,
    message,
    status,
    isRetryable,
    isNetworkError,
  };
};

// 延遲函數
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// 創建 axios 實例
export const apiClient: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  withCredentials: true,
});

// 請求攔截器（附帶 token）
apiClient.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    // 請求日誌
    console.log(`🚀 API 請求: ${config.method?.toUpperCase()} ${config.url}`);

    const newConfig: AxiosRequestConfig = {
      ...config,
      headers: {
        ...(config.headers as AxiosRequestHeaders),
        // 添加認證 token
        Authorization: getCookie(CookieNameEnum.AUTH_TOKEN) || '',
      },
      withCredentials: true,
    };

    return newConfig as InternalAxiosRequestConfig;
  },
  (error: AxiosError) => {
    console.error('請求攔截器錯誤:', error);
    return Promise.reject(error);
  },
);

// 響應攔截器（帶重試機制）
apiClient.interceptors.response.use(
  response => {
    // 成功響應處理
    console.log(`✅ API 響應: ${response.status} ${response.config.url}`);

    const contentType = response.headers['content-type'] || '';

    // 如果 content-type 包含 application/json，則轉換為 camelCase
    if (contentType.includes('application/json')) {
      response.data = toCamelCase(response.data);
    }

    return response.data; // 直接返回 data，簡化調用
  },
  async (error: AxiosError) => {
    const originalRequest = error.config as InternalAxiosRequestConfig & { _retryCount?: number };

    // 錯誤響應處理
    console.error('❌ API 請求失敗:', {
      url: error.config?.url,
      method: error.config?.method,
      status: error.response?.status,
      message: error.message,
      retryCount: originalRequest._retryCount || 0,
    });

    // 創建標準化錯誤對象
    const apiError = createApiError(error);

    // 檢查是否應該重試
    if (
      originalRequest &&
      apiError.isRetryable &&
      (!originalRequest._retryCount || originalRequest._retryCount < RETRY_CONFIG.maxRetries)
    ) {
      // 初始化重試計數
      originalRequest._retryCount = (originalRequest._retryCount || 0) + 1;

      // 計算延遲時間（指數退避）
      const delayTime = RETRY_CONFIG.retryDelay * Math.pow(2, originalRequest._retryCount - 1);

      console.log(
        `🔄 重試請求 (${originalRequest._retryCount}/${RETRY_CONFIG.maxRetries}): ${originalRequest.url}`,
      );

      // 等待後重試
      await delay(delayTime);

      try {
        return await apiClient.request(originalRequest);
      } catch (retryError) {
        // 如果重試也失敗，記錄錯誤並繼續執行後續錯誤處理
        console.error(`❌ 重試失敗: ${originalRequest.url}`, retryError);
        // 重試失敗後，繼續執行下面的錯誤處理邏輯
      }
    }

    // 特殊處理：401 錯誤時清除認證信息
    if (apiError.status === 401) {
      // TODO: 清除用戶認證狀態，重定向到登入頁面
      // clearAuthState();
      // window.location.href = '/login';
    }

    // 拋出標準化錯誤
    const enhancedError = new Error(apiError.message) as Error & { apiError: ApiError };
    enhancedError.apiError = apiError;

    return Promise.reject(enhancedError);
  },
);

// 導出錯誤處理工具
export { createApiError, RETRY_CONFIG };

export default apiClient;
